package com.dateup.android.ui

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import com.dateup.android.utils.EdgeToEdgeUtils
import com.dateup.android.utils.applyTopInsetPadding
import com.dateup.android.R

open class BaseEdgeToEdgeActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // Restore reliable adjustResize behavior across OEMs
        WindowCompat.setDecorFitsSystemWindows(window, true)
        super.onCreate(savedInstanceState)
    }

    override fun setContentView(layoutResID: Int) {
        super.setContentView(layoutResID)
        applyWindowInsets()
    }

    override fun setContentView(view: View?) {
        super.setContentView(view)
        applyWindowInsets()
    }

    override fun setContentView(view: View, params: ViewGroup.LayoutParams?) {
        super.setContentView(view, params)
        applyWindowInsets()
    }

    protected open fun applyWindowInsets() {
        val content = findViewById<ViewGroup>(android.R.id.content)
        val root = content?.getChildAt(0) ?: return
        // 1) Top insets: if there is a Toolbar or a custom_action_bar container, pad it; else pad root
        val topBarContainer = root.findViewById<View?>(R.id.custom_action_bar)
        val toolbar = root.findViewById<View?>(R.id.toolbar)
        when {
            topBarContainer != null -> {
                applyTopInsetPadding(topBarContainer)
                EdgeToEdgeUtils.applySystemBarsAndImeInsets(root, applyTop = false)
            }
            toolbar != null -> {
                applyTopInsetPadding(toolbar)
                EdgeToEdgeUtils.applySystemBarsAndImeInsets(root, applyTop = false)
            }
            else -> {
                // No toolbar found: apply top on root as fallback
                EdgeToEdgeUtils.applySystemBarsAndImeInsets(root, applyTop = true)
            }
        }
    }
}

