apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'

android {

    namespace 'com.dateup.android'
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        compileSdk 36
        minSdkVersion 26
        targetSdkVersion 36
        versionCode 166
        versionName generateVersionName()
        resourceConfigurations += ['en']
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            storeFile file('dateup-release')
            storePassword "ThaiCurryWings2018"
            keyAlias "DateUpRelease1"
            keyPassword "ThaiCurryWingsExtraWet2018**"
        }
    }

    buildTypes {

        release {
            minifyEnabled true
            shrinkResources true
            debuggable false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // Generate native symbols that gets uploaded to playstore
            ndk {
                debugSymbolLevel = 'FULL'
                // Support for 16KB memory page sizes
                abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
            }


        }
        debug {
            versionNameSuffix "-SNAPSHOT"
            debuggable true
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // Generate native symbols that gets uploaded to playstore
            ndk {
                debugSymbolLevel = 'FULL'
                // Support for 16KB memory page sizes
                abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
            }


        }

    }

    buildFeatures {
        viewBinding true
        buildConfig true
    }
    lint {
        checkReleaseBuilds true
    }

    packagingOptions {
        // Use legacy packaging to compress native libraries and avoid 16KB page alignment issues
        jniLibs {
            useLegacyPackaging = true
        }
    }

}

private static String generateVersionName() {
    Integer versionMajor = 8
    Integer versionMinor = 0
    Integer versionPatch = 0
    Integer buildNumber = 7
    return versionMajor + "." + versionMinor + "." + versionPatch + "(" + buildNumber + ")"
}

dependencies {
    // Jar libs
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Support libs
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // ViewModel and LiveData extensions.
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.9.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2'

    implementation 'androidx.room:room-runtime:2.7.2'
    kapt 'androidx.room:room-compiler:2.7.2'
    implementation 'androidx.room:room-ktx:2.7.2'
    implementation 'androidx.room:room-rxjava2:2.7.2'
    implementation 'androidx.cardview:cardview:1.0.0'

    implementation 'com.tbuonomo:dotsindicator:5.1.0'



    //workmanager using for glide
    implementation 'androidx.work:work-runtime-ktx:2.10.3'

    //Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

    //Lottie
    implementation 'com.airbnb.android:lottie:6.6.7'

    //segmented control
    implementation 'com.github.ceryle:SegmentedButton:v2.0.2'

    //multislider
    implementation 'io.apptik.widget:multislider:1.3'

    //location
    implementation 'com.google.android.gms:play-services-location:21.3.0'

    //geofire
    implementation 'com.firebase:geofire-android:3.2.0'

    //chat kit
    implementation('com.github.stfalcon-studio:Chatkit:0.4.1') {
        exclude group: 'com.google.android', module: 'flexbox'
    }

    // Add the new flexbox library to replace the excluded one
    implementation 'com.google.android.flexbox:flexbox:3.0.0'
    //Timber
    implementation 'com.jakewharton.timber:timber:5.0.1'

    //https://github.com/DanielMartinus/Konfetti
    implementation 'nl.dionsegijn:konfetti:1.3.2'

    //google play billing
    implementation "com.android.billingclient:billing-ktx:8.0.0"

    implementation 'androidx.browser:browser:1.9.0'

    implementation 'com.google.code.gson:gson:2.13.1'

    // For photo orientation
    implementation 'androidx.exifinterface:exifinterface:1.4.1'

    // Import the BoM for the Firebase platform
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation platform('com.google.firebase:firebase-bom:34.1.0')

    // Analytics
    implementation 'com.google.firebase:firebase-analytics-ktx'
    // Firebase functions
    implementation 'com.google.firebase:firebase-functions-ktx'
    // dynamic links
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    // Performance
    implementation 'com.google.firebase:firebase-perf-ktx'
    // In-App messaging
    implementation 'com.google.firebase:firebase-inappmessaging-display-ktx'
    //Firebase
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'

    // Firebase ML Kit
    implementation 'com.google.mlkit:barcode-scanning:17.3.0'
    implementation 'com.google.mlkit:object-detection-custom:17.0.2'

    // AWS S3 SDK
    implementation 'com.amazonaws:aws-android-sdk-s3:2.81.0'

    // https://github.com/Shouheng88/Compressor
    // Image compression library
    implementation 'com.github.Shouheng88:compressor:1.6.0'


    // https://github.com/jkwiecien/EasyImage
    implementation 'com.github.jkwiecien:EasyImage:3.2.0'

    // library used for app update
    // This dependency is downloaded from the Google’s Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    implementation 'com.google.android.play:app-update:2.1.0'
    // Play Services
    implementation 'com.google.android.gms:play-services-maps:19.2.0'
    implementation 'com.google.android.gms:play-services-auth:21.4.0'


    // For Kotlin users, also add the Kotlin extensions library for Play In-App Update:
    implementation 'com.google.android.play:app-update-ktx:2.1.0'

    // app check
    implementation 'com.google.firebase:firebase-appcheck-debug:19.0.0'
    implementation("com.google.firebase:firebase-appcheck-playintegrity")

    // places
    implementation 'com.google.android.libraries.places:places:4.4.1'

    // ucrop https://github.com/Yalantis/uCrop
    implementation 'com.github.yalantis:ucrop:2.2.11'

    //Branch.io
    implementation 'io.branch.sdk.android:library:5.20.0'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'

    // leakcanary
    // debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.7'

    // facebook
    implementation 'com.facebook.android:facebook-login:18.1.3'

    // Google Play Install Referrer API
    implementation 'com.android.installreferrer:installreferrer:2.2'

    // Meta Ads SDK for attribution
    implementation 'com.facebook.android:facebook-android-sdk:18.1.3'
}

apply plugin: 'com.google.gms.google-services'
